using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Newtonsoft.Json;
using Serilog;
using Zin.Application.DTOs.Importacao;
using Zin.Application.Services.Importacoes;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Application.Services.Processos.Interfaces;
using Zin.Domain.Entidades.Importacoes;
using Zin.Domain.Enums;
using Zin.Domain.Repositorios.Importacoes;
using Zin.Domain.Repositorios.ZinPag;

namespace Zin.Application.Tests.Importacao.Service
{
    public class ProcessamentoImportacaoServiceTests
    {
        private readonly Mock<IHttpContextAccessor> _httpAccessor = new();
        private readonly Mock<IServiceScopeFactory> _factoryMock = new();
        private readonly Mock<IImportacaoAgregadorRepository> _importacaoRepo = new();
        private readonly Mock<IFilaProcessamentoRepository> _filaProcessamentoRepository = new();
        private readonly Mock<IImportacaoAgregadorService> _agregadorService = new();
        private readonly Mock<IProcessosService> _processosService = new();
        private readonly Mock<ILogger> _logger = new();

        public ProcessamentoImportacaoServiceTests()
        {
         
        }

        private ProcessamentoImportacaoService CreateService()
        { 
            return new ProcessamentoImportacaoService
            (
            _httpAccessor.Object,
            _factoryMock.Object,
            _importacaoRepo.Object,
            _filaProcessamentoRepository.Object,
            _agregadorService.Object,
            _processosService.Object,
            _logger.Object
            );
        }

        [Fact]
        public async Task ProcessaImportacaoAgregadorAsync_ProcessaImportacoesPendentes_ChamaAtualizaImportacaoEInserirAgregador()
        {
            // Arrange
            var service = CreateService();
            var dto = new ImportacaoAgregadorDTO
            {
                TipoAgregador = (TipoAgregador)100,
                Numero = "AG999",
                ClienteEmpresa = new ImportarClienteEmpresaDTO
                {
                    Cnpj = "99999999000199",
                    RazaoSocial = "Empresa Processamento",
                    NomeFantasia = "Fantasia Processamento",
                    Principal = true,
                    Endereco = new ImportarEnderecoDTO
                    {
                        Logradouro = "Rua Processamento",
                        Numero = "999",
                        Complemento = "Sala 9",
                        Bairro = "Centro",
                        CodIbgeCidade = "3550308", // C�digo IBGE v�lido para S�o Paulo
                        Cep = "01000999"
                    }
                },
                //Ativos = new List<ImportarAtivosDTO>(),
                Fornecedores = new List<ImportarPessoaJuridicaDTO>(),
                Itens = new List<ImportarItemDTO>()
            };

            var importacao = ImportacaoAgregador.CriarAguardandoProcessamento(JsonConvert.SerializeObject(dto), "");

            //_importacaoRepo.Setup(r => r.BuscarImportacoesPendentesPorClienteAsync(""))
            //    .ReturnsAsync(new List<ImportacaoAgregador> { importacao });
            //_agregadorRepo.Setup(r => r.BuscarPorNumeroAsync(It.IsAny<string>()))
            //    .ReturnsAsync((Agregador?)null);
            //_pessoaRepo.Setup(r => r.BuscarAsync(It.IsAny<Expression<Func<Pessoa, bool>>>()))
            //    .ReturnsAsync(new List<Pessoa>());
            //_pessoaRepo.Setup(r => r.InserirAsync(It.IsAny<Pessoa>())).ReturnsAsync(1);
            //_agregadorRepo.Setup(r => r.InserirAsync(It.IsAny<Agregador>())).ReturnsAsync(1);
            //_agregadorRepo.Setup(r => r.BuscarPorIdAsync(It.IsAny<int>())).ReturnsAsync(
            //    new AgregadorBuilder()
            //        .Build()                
            //);
            _importacaoRepo.Setup(r => r.AtualizarAsync(It.IsAny<ImportacaoAgregador>())).Returns(Task.CompletedTask);

            // Act
            await service.ProcessaImportacaoAgregadorTodosClientesAsync(10);

            // Assert
            // Deve ao menos chamar o m�todo de atualiza��o da importa��o e o m�todo de inser��o do agregador
            _importacaoRepo.Verify(r => r.AtualizarAsync(It.IsAny<ImportacaoAgregador>()), Times.Once);
            //_agregadorRepo.Verify(r => r.InserirAsync(It.IsAny<Agregador>()), Times.Once);
        }
    }
}
