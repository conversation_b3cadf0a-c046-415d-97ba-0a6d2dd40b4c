using FluentValidation;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using System.Text;
using Zin.Api.Filters;
using Zin.Application.Helpers.Validador;
using Zin.Application.Interfaces;
using Zin.Application.Mappings;
using Zin.Application.Mock;
using Zin.Application.Services.Cadastros;
using Zin.Application.Services.Cadastros.Interfaces;
using Zin.Application.Services.Cadastros.Regras;
using Zin.Application.Services.Importacoes;
using Zin.Application.Services.Importacoes.Interfaces;
using Zin.Application.Services.Processos;
using Zin.Application.Services.Processos.Interfaces;
using Zin.Application.Services.ZinPag;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Domain.Factories;
using Zin.Domain.Factories.Interfaces;
using Zin.Domain.Repositorios.Cadastros;
using Zin.Domain.Repositorios.Importacoes;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;
using Zin.Infrastructure.Dados.Factories;
using Zin.Infrastructure.Repositorios.Cadastros;
using Zin.Infrastructure.Repositorios.Importacoes;
using Zin.Infrastructure.Repositorios.ZinPag;
using Zin.Infrastructure.Services;
using Zin.Infrastructure.UnitOfWork;

namespace Zin.Api.Extensions
{
    public static class ServiceExtensions
    {
        public static IServiceCollection AdicionaServicosCustomizados(
            this IServiceCollection services,
            IConfiguration configuration,
            Serilog.ILogger logger)
        {
            // adiciona CORS policy antes de tudo
            AddCorsPolicy(services, configuration);

            services.AddHttpClient();

            services.AddEndpointsApiExplorer();

            services.AddControllers(options =>
            {
                options.Filters.Add(new Microsoft.AspNetCore.Mvc.Authorization.AuthorizeFilter("Bearer"));
            });

            AdicionaSwagger(services);
            AdicionaAutenticacao(services, configuration);
            AdicionaUnitOfWorks(services);
            AdicionaRepositorios(services);
            AdicionaFactories(services);
            AdicionaServices(services, configuration);
            AdicionaMappings(services);
            AdicionaValidadores(services);
            AdicionaDbContextFactory(services, configuration, logger);

            // Executa apenas em ambiente de desenvolvimento
            var serviceProvider = services.BuildServiceProvider();
            var env = serviceProvider.GetRequiredService<IHostEnvironment>();
            if (env.IsDevelopment())
            {
                // Cria o DbContext apenas para uso em design time, como migrations
                _ = new ZinDbContextDesignTimeFactory().CreateDbContext(null!);

                // TODO: Migration para Importações ainda não esta funcional
                // Cria o DbContext de importações
                //_ = new ZinImportacoesDbContextDesignTimeFactory().CreateDbContext(null!);
            }

            return services;
        }

        private static IServiceCollection AdicionaSwagger(this IServiceCollection services)
        {
            return services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "SMART WEB API", Version = "v1" });

                // Configurar a autenticação JWT no Swagger
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.Http,
                    Scheme = "Bearer"
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        new string[] {}
                    }
                });

                c.OperationFilter<TenantHeaderOperationFilter>();
            });
        }

        private static IServiceCollection AdicionaAutenticacao(this IServiceCollection services, IConfiguration configuration)
        {
            // Configurar autenticação JWT
            var jwtSettings = configuration.GetSection("JwtSettings");
            var key = Encoding.ASCII.GetBytes(jwtSettings["Secret"] ?? throw new Exception("Chave Secreta do JWT não esta configurada"));
            var audience = jwtSettings["Audience"] ?? throw new Exception("Audiencia do JWT não esta configurada");
            var issuer = jwtSettings["Issuer"] ?? throw new Exception("Emissor do JWT não esta configurada");

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = issuer,
                    ValidAudience = audience,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ClockSkew = TimeSpan.Zero // Adicionado para garantir que não haja diferença de tempo
                };
            });

            services.AddAuthorizationBuilder()
            .AddPolicy("Bearer", policy =>
            {
                policy.AddAuthenticationSchemes(JwtBearerDefaults.AuthenticationScheme);
                policy.RequireAuthenticatedUser();
            });

            return services;
        }

        private static IServiceCollection AdicionaUnitOfWorks(this IServiceCollection services)
        {
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IImportacaoUnitOfWork, ImportacaoUnitOfWork>();

            return services;
        }

        private static IServiceCollection AdicionaRepositorios(this IServiceCollection services)
        {
            services.AddScoped<IAgregadorRepository, AgregadorRepository>();
            services.AddScoped<IPessoaRepository, PessoaRepository>();
            services.AddScoped<IVeiculoRepository, VeiculoRepository>();
            services.AddScoped<IAtivoRepository, AtivoRepository>();
            services.AddScoped<IPagamentoRepository, PagamentoRepository>();
            services.AddScoped<IRessarcimentoRepository, RessarcimentoRepository>();
            services.AddScoped<IItemRepository, ItemRepository>();
            services.AddScoped<IItemVersaoRepository, ItemVersaoRepository>();
            services.AddScoped<INotafiscalRepository, NotaFiscalRepository>();
            services.AddScoped<IDocumentoRepository, DocumentoRepository>();
            services.AddScoped<IDocumentoPagamentoRepository, DocumentoPagamentoRepository>();
            services.AddScoped<IRegistroProcessamentoRepository, RegistroProcessamentoRepository>();
            services.AddScoped<IProcessamentoStatusRepository, ProcessamentoStatusRepository>();
            services.AddScoped<IRegistroProcessamentoRepository, RegistroProcessamentoRepository>();
            services.AddScoped<ILiquidacaoPagamentoRepository, LiquidacaoPagamentoRepository>();
            services.AddScoped<IConfiguracaoRepository, ConfiguracaoRepository>();
            services.AddScoped<ICondicaoComparadorRepository, CondicaoComparadorRepository>();
            services.AddScoped<IMovimentacoesRepository, MovimentacoesRepository>();
            services.AddScoped<IFilaProcessamentoRepository, FilaProcessamentoRepository>();
            services.AddScoped<IDashboardRepository, DashboardRepository>();
            services.AddScoped<IFilaProcessamentoHistoricoRepository, FilaProcessamentoHistoricoRepository>();
            services.AddScoped<IEstadoRepository, EstadoRepository>();
            services.AddScoped<ICidadeRepository, CidadeRepository>();
            services.AddScoped<IEnderecoRepository, EnderecoRepository>();
            services.AddScoped<IContatoRepository, ContatoRepository>();
            services.AddScoped<IDadoBancarioRepository, DadoBancarioRepository>();
            services.AddScoped<IBancoRepository, BancoRepository>();
            services.AddScoped<ILiquidacaoRessarcimentoRepository, LiquidacaoRessarcimentoRepository>();
            services.AddScoped<IDocumentoRessarcimentoRepository, DocumentoRessarcimentoRepository>();
            services.AddScoped<IRessarcimentoItemVersaoRepository, RessarcimentoItemVersaoRepository>();
            services.AddScoped<IPessoaJuridicaRepository, PessoaJuridicaRepository>();
            services.AddScoped<IClienteEmpresaRepository, ClienteEmpresaRepository>();

            services.AddScoped<IImportacaoRepository, ImportacaoRepository>();
            services.AddScoped<IImportacaoPagamentoLinhaRepository, ImportacaoPagamentoLinhaRepository>();

            return services;
        }


        private static void AdicionaFactories(IServiceCollection services)
        {
            services.AddScoped<IItemVersaoFactory, ItemVersaoFactory>();
        }

        private static IServiceCollection AdicionaServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<Application.Configuration.NotificationServiceConfig>(configuration.GetSection("NotificationService"));

            var usarDadosMockados = configuration.GetValue<bool>("UsarDadosMockados");

            if (usarDadosMockados)
            {
                // Usa o serviço mockado
                //services.AddScoped<IMovimentacoesService, MovimentacoesMockService>();
                services.AddScoped<IMovimentacoesService, MovimentacoesService>();
                services.AddScoped<IPaineisService, PaineisMockService>();
            }
            else
            {
                // Usa o serviço real
                services.AddScoped<IMovimentacoesService, MovimentacoesService>();
                services.AddScoped<IPaineisService, PaineisService>();
            }

            // Serviços ainda sem mock
            services.AddScoped<IAgregadorService, Application.Services.ZinPag.AgregadorService>();
            services.AddScoped<IImportacaoService, ImportacaoService>();
            services.AddScoped<Application.Services.Cadastros.Interfaces.IPessoaService, PessoaService>();
            services.AddScoped<Application.Services.Cadastros.Interfaces.IClienteEmpresaService, Application.Services.Cadastros.ClienteEmpresaService>();
            services.AddScoped<Application.Services.ZinPag.Interfaces.IVeiculoService, Application.Services.ZinPag.VeiculoService>();
            services.AddScoped<IPagamentoService, PagamentoService>();
            services.AddScoped<Application.Services.ZinPag.Interfaces.IItemService, Application.Services.ZinPag.ItemService>();
            services.AddScoped<IItemVersaoService, ItemVersaoService>();
            services.AddScoped<IRessarcimentoService, RessarcimentoService>();
            services.AddScoped<Application.Services.ZinPag.Interfaces.IDocumentoService, Application.Services.ZinPag.DocumentoService>();
            services.AddScoped<IProcessosService, ProcessosService>();
            services.AddScoped<IPossivelItemDuplicadoService, PossivelItemDuplicadoService>();
            services.AddScoped<IPossivelPagamentoDuplicadoService, PossivelPagamentoDuplicadoService>();
            services.AddScoped<IPossivelPagamentoService, PossivelPagamentoService>();
            services.AddScoped<IPossivelRessarcimentoService, PossivelRessarcimentoService>();
            services.AddScoped<IRegistroProcessamentoService, RegistroProcessamentoService>();
            services.AddScoped<Application.Services.Importacoes.Interfaces.IConfiguracaoService, ConfiguracaoService>();
            services.AddScoped<Application.Services.Importacoes.Interfaces.ICondicaoComparadorService, CondicaoComparadorService>();
            services.AddScoped<IEstadoService, EstadoService>();
            services.AddScoped<ICidadeService, CidadeService>();
            services.AddScoped<IEnderecoService, EnderecoService>();
            services.AddScoped<IContatoService, ContatoService>();
            services.AddScoped<IDadoBancarioService, DadoBancarioService>();
            services.AddScoped<IBancoService, BancoService>();

            services.AddScoped<Application.Services.Importacoes.Interfaces.IConfiguracaoService, ConfiguracaoService>();
            services.AddScoped<Application.Services.Importacoes.Interfaces.ICondicaoComparadorService, CondicaoComparadorService>();

            services.AddScoped<Application.Services.Cadastros.Interfaces.IFornecedorService, Application.Services.Cadastros.FornecedorService>();
            services.AddScoped<Application.Services.Cadastros.Interfaces.IOficinaService, Application.Services.Cadastros.OficinaService>();

            services.AddScoped<IProcessamentoImportacaoService, ProcessamentoImportacaoService>();

            services.AddScoped<PossivelItemDuplicadoService>();
            services.AddScoped<PossivelPagamentoDuplicadoService>();
            services.AddScoped<PossivelPagamentoService>();
            services.AddScoped<PossivelRessarcimentoService>();

            if (usarDadosMockados)
            {
                services.AddScoped<Application.Services.ZinPag.IPagamentoImportacaoService, PagamentoImportacaoMockService>();
            }
            else
            {
                services.AddScoped<Application.Services.ZinPag.IPagamentoImportacaoService, PagamentoImportacaoService>();
            }
            services.AddScoped<IFilaProcessamentoService, FilaProcessamentoService>();
            services.AddScoped<IDashboardService, DashboardService>();
            services.AddScoped<IExcelMappingConfigurationProvider, ExcelMappingConfigurationProvider>();

            services.AddScoped<IImportacaoFactory, ImportacaoFactory>();
            services.AddScoped<IImportarPessoaJuridicaService, ImportarPessoaJuridicaService>();
            services.AddScoped<IImportarClienteEmpresaService, ImportarClienteEmpresaService>();
            services.AddScoped<IImportarAtivoVeiculoService, ImportarAtivoVeiculoService>();
            services.AddScoped<IImportarDocumentoService, ImportarDocumentoService>();
            services.AddScoped<IImportarItensService, ImportarItensService>();
            services.AddScoped<IImportacaoAgregadorService, ImportacaoAgregadorService>();
            services.AddScoped<IProcessamentoImportacaoService, ProcessamentoImportacaoService>();

            return services;
        }

        private static IServiceCollection AdicionaMappings(this IServiceCollection services)
        {
            services.AddAutoMapper(cfg =>
            {
                cfg.AddProfile<AgregadorMappingProfile>();
                cfg.AddProfile<ImportacaoMappingProfile>();
                cfg.AddProfile<CadastrosMappingProfile>();
                cfg.AddProfile<ClienteEmpresaMappingProfile>();
                cfg.AddProfile<ItemMappingProfile>();
                cfg.AddProfile<ItemVersaoMappingProfile>();
                cfg.AddProfile<DocumentoMappingProfile>();
                cfg.AddProfile<ZinPagMappingProfile>();
                cfg.AddProfile<ConfiguracaoMappingProfile>();
                cfg.AddProfile<RegraMappingProfile>();
                cfg.AddProfile<ImportarNotaFiscalDetalhadoProfile>();
            }, AppDomain.CurrentDomain.GetAssemblies());

            return services;
        }

        private static IServiceCollection AdicionaDbContextFactory(this IServiceCollection services, IConfiguration configuration, Serilog.ILogger logger)
        {
            // Serviços necessários para o DbContextFactory
            services.AddHttpContextAccessor();
            services.AddScoped<IClienteProvider, ClienteProvider>();
            services.AddScoped<IClienteConnectionService, ClienteConnectionService>();

            // Configuração do DbContextFactory
            services.AddScoped<IDbContextFactory<ZinDbContext>, ZinDbContextFactory>();

            var connString = configuration.GetConnectionString("BancoZinImportacoes");

            if (string.IsNullOrEmpty(connString))
            {
                throw new InvalidOperationException("A string de conexão 'BancoZinImportacoes' não foi encontrada no appsettings.json nem nos user secrets.");
            }

            services.AddDbContextFactory<ZinImportacoesDbContext>(options =>
            {
                options.UseNpgsql(connString);
            });

            return services;
        }

        private static IServiceCollection AddCorsPolicy(this IServiceCollection services, IConfiguration configuration)
        {
            try
            {
                var corsSection = configuration.GetSection("CorsSettings");
                var allowedOrigins = corsSection.GetSection("AllowedOrigins").Get<string[]>();

                if (allowedOrigins == null || allowedOrigins.Length == 0)
                {
                    throw new InvalidOperationException("Nenhuma origem permitida configurada no appsettings.json");
                }

                Log.Logger.Information("Configuração de CORS encontrada: {AllowedOrigins}", string.Join(", ", allowedOrigins));

                services.AddCors(options =>
                {
                    options.AddPolicy("DefaultCorsPolicy", builder =>
                    {
                        builder
                            .WithOrigins(allowedOrigins)
                            .AllowAnyHeader()
                            .AllowAnyMethod()
                            .AllowCredentials();
                    });
                });

                Log.Logger.Information("CORS configurado com sucesso.");
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "Erro ao configurar o CORS");
                throw;
            }

            return services;
        }

        private static IServiceCollection AdicionaValidadores(this IServiceCollection services)
        {
            services.AddValidatorsFromAssemblyContaining<ValidadorCriaImportacaoAgregador>(
                includeInternalTypes: true,
                lifetime: ServiceLifetime.Scoped
            );

            return services;
        }
    }

    public class ZinDbContextDesignTimeFactory : IDesignTimeDbContextFactory<ZinDbContext>
    {
        public ZinDbContext CreateDbContext(string[] args)
        {
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";

            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile($"appsettings.{environment}.json", optional: true)
                .AddUserSecrets<ZinDbContextDesignTimeFactory>(optional: true)
                .AddEnvironmentVariables()
                .Build();

            var connectionString = configuration.GetConnectionString("DefaultConnection");

            if (string.IsNullOrEmpty(connectionString))
            {
                // Tenta buscar diretamente dos user secrets se não encontrou no appsettings
                connectionString = configuration["ConnectionStrings:DefaultConnection"];
            }

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("A string de conexão 'DefaultConnection' não foi encontrada no appsettings.json nem nos user secrets.");
            }

            var optionsBuilder = new DbContextOptionsBuilder<ZinDbContext>();
            optionsBuilder.UseNpgsql(connectionString);

            return new ZinDbContext(optionsBuilder.Options);
        }
    }

    public class ZinImportacoesDbContextDesignTimeFactory : IDesignTimeDbContextFactory<ZinImportacoesDbContext>
    {
        public ZinImportacoesDbContext CreateDbContext(string[] args)
        {
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";

            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile($"appsettings.{environment}.json", optional: true)
                .AddUserSecrets<ZinDbContextDesignTimeFactory>(optional: true)
                .AddEnvironmentVariables()
                .Build();

            var connectionString = configuration.GetConnectionString("ZinImportacoesDb");

            if (string.IsNullOrEmpty(connectionString))
            {
                // Tenta buscar diretamente dos user secrets se não encontrou no appsettings
                connectionString = configuration["ConnectionStrings:ZinImportacoesDb"];
            }

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("A string de conexão 'ZinImportacoesDb' não foi encontrada no appsettings.json nem nos user secrets.");
            }

            var optionsBuilder = new DbContextOptionsBuilder<ZinImportacoesDbContext>();
            optionsBuilder.UseNpgsql(connectionString);

            return new ZinImportacoesDbContext(optionsBuilder.Options);
        }
    }
}