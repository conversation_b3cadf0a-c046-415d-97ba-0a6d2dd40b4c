using Microsoft.AspNetCore.Mvc;
using Zin.Api.Shared;
using Zin.Application.DTOs.Importacao.Pagamento;
using Zin.Application.DTOs.Pagamentos;
using Zin.Application.Services.ZinPag;
using Zin.Application.Services.ZinPag.Interfaces;
using Zin.Helpers.Clientes.Models;

namespace Zin.Api.Controllers.ZinPag
{
    [Route("pagamentos")]
    [ApiController]
    public class PagamentosController(
        IPagamentoService pagamentoService,
        Application.Services.ZinPag.IPagamentoImportacaoService pagamentoImportacaoService,
        IExcelMappingConfigurationProvider excelMappingConfigurationProvider) : ControllerBase
    {
        public IPagamentoService _pagamentoService { get; } = pagamentoService;
        public Application.Services.ZinPag.IPagamentoImportacaoService _pagamentoImportacaoService { get; } = pagamentoImportacaoService;
        public IExcelMappingConfigurationProvider _excelMappingConfigurationProvider { get; } = excelMappingConfigurationProvider;

        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CriaPagamentoDTO dto)
        {
            var id = await _pagamentoService.CriarPagamentoAsync(dto);
            return Ok(id);
        }

        // TODO: Implementar upload de Excel para importação de pagamentos
        // [HttpPost("importacoes/excel")]

        [HttpGet("importacoes/lotes")]
        public async Task<IActionResult> ListarLotes([FromQuery] FiltroLoteInput filtro)
        {
            var resultado = await _pagamentoImportacaoService.ListarLotesAsync(filtro);
            return this.ToActionResult(resultado);
        }

        [HttpGet("importacoes/lotes/{loteId}")]
        public async Task<IActionResult> ObterDetalhesLote(int loteId)
        {
            var resultado = await _pagamentoImportacaoService.ObterDetalhesLoteAsync(loteId);
            return this.ToActionResult(resultado);
        }

        [HttpGet("importacoes/lotes/{loteId}/linhas")]
        public async Task<IActionResult> ListarLinhasDoLote(int loteId, [FromQuery] FiltroLinhaInput filtro)
        {
            var resultado = await _pagamentoImportacaoService.ListarLinhasDoLoteAsync(loteId, filtro);
            return this.ToActionResult(resultado);
        }

        [HttpPost("cancelar/{pagamentoId:int}")]
        public async Task<IActionResult> Cancelar(int pagamentoId)
        {
            var resultado = await _pagamentoService.CancelarPagamentoAsync(pagamentoId);
            return Ok(resultado);
        }

        [HttpPost("liquidar/{pagamentoId:int}")]
        public async Task<IActionResult> LiquidarPagamento(int pagamentoId, decimal valorPago)
        {
            var resultado = await _pagamentoService.ConfirmarPagamentoManualAsync(pagamentoId, valorPago);
            return Ok(resultado);
        }

        [HttpGet("liquidacoes/{liquidacaoId:int}/comprovante")]
        public async Task<IActionResult> DownloadComprovanteLiquidacao(int liquidacaoId)
        {
            var result = await _pagamentoService.DownloadComprovanteLiquidacaoAsync(liquidacaoId);

            if (result == null)
            {
                return NotFound("Comprovante não encontrado ou liquidacao sem comprovante.");
            }

            var (fileContents, contentType, fileName) = result.Value;
            return File(fileContents, contentType, fileName);
        }
    }
}