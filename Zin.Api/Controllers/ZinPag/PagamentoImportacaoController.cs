using Microsoft.AspNetCore.Mvc;
using Zin.Api.Shared;
using Zin.Application.DTOs.Importacao.Pagamento;
using Zin.Application.Services.ZinPag;

namespace Zin.Api.Controllers.ZinPag;

[ApiController]
[Route("api/pagamentos/importacoes")]
public class PagamentoImportacaoController : ControllerBase
{
    private readonly IPagamentoImportacaoService _service;

    public PagamentoImportacaoController(IPagamentoImportacaoService service)
    {
        _service = service;
    }

    [HttpGet("lotes")]
    public async Task<IActionResult> ListarLotes([FromQuery] FiltroLoteInput filtro)
    {
        var resultado = await _service.ListarLotesAsync(filtro);
        return this.ToActionResult(resultado);
    }

    [HttpGet("lotes/{loteId}")]
    public async Task<IActionResult> ObterDetalhesLote(int loteId)
    {
        var resultado = await _service.ObterDetalhesLoteAsync(loteId);
        return this.ToActionResult(resultado);
    }

    [HttpGet("lotes/{loteId}/linhas")]
    public async Task<IActionResult> ListarLinhasDoLote(int loteId, [FromQuery] FiltroLinhaInput filtro)
    {
        var resultado = await _service.ListarLinhasDoLoteAsync(loteId, filtro);
        return this.ToActionResult(resultado);
    }
}
