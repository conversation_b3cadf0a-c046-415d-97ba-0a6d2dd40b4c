using Zin.Application.DTOs.Importacao.Pagamento;
using Zin.Application.Services.ZinPag;
using Zin.Application.Shared.Retorno;

namespace Zin.Application.Mock;

public class PagamentoImportacaoMockService : IPagamentoImportacaoService
{
    public Task<ResultadoApp<List<LotePagamentoItemOutput>>> ListarLotesAsync(FiltroLoteInput filtro)
    {
        var query = PagamentoImportacaoMockDb.Lotes.AsQueryable();

        if (filtro.DataInicio.HasValue)
            query = query.Where(x => x.Data >= filtro.DataInicio.Value);
        if (filtro.DataFim.HasValue)
            query = query.Where(x => x.Data <= filtro.DataFim.Value);
        if (!string.IsNullOrEmpty(filtro.Status))
            query = query.Where(x => x.Status == filtro.Status);
        if (!string.IsNullOrEmpty(filtro.Usuario))
            query = query.Where(x => x.Usuario.Contains(filtro.Usuario));

        var items = query.OrderByDescending(x => x.Data).ToList();
        var result = ResultadoApp<List<LotePagamentoItemOutput>>.OK(items);
        return Task.FromResult(result);
    }

    public Task<ResultadoApp<LotePagamentoDetalheOutput>> ObterDetalhesLoteAsync(int loteId)
    {
        var lote = PagamentoImportacaoMockDb.LoteDetalhes.FirstOrDefault(l => l.Id == loteId);
        
        if (lote == null)
            return Task.FromResult(ResultadoApp<LotePagamentoDetalheOutput>.Falha(new ErroApp(TipoErroApp.NaoEncontrado, "LOTE_NAO_ENCONTRADO", "Lote não encontrado")));

        var result = ResultadoApp<LotePagamentoDetalheOutput>.OK(lote);
        return Task.FromResult(result);
    }

    public Task<ResultadoApp<List<LotePagamentoLinhaOutput>>> ListarLinhasDoLoteAsync(int loteId, FiltroLinhaInput filtro)
    {
        // retorna todas as linhas de exemplo, mas aplicando os filtros
        var query = PagamentoImportacaoMockDb.Linhas.AsQueryable();

        if (!string.IsNullOrEmpty(filtro.Status))
            query = query.Where(x => x.Resultado == filtro.Status);
        if (!string.IsNullOrEmpty(filtro.Operacao))
            query = query.Where(x => x.OperacaoAplicada == filtro.Operacao);
        if (!string.IsNullOrEmpty(filtro.NumeroNF))
            query = query.Where(x => x.NumeroNF.Contains(filtro.NumeroNF));
        if (!string.IsNullOrEmpty(filtro.Cnpj))
            query = query.Where(x => x.CnpjPagador.Contains(filtro.Cnpj) || x.CnpjCpfFavorecido.Contains(filtro.Cnpj));

        var items = query.OrderBy(x => x.NumeroLinha).ToList();
        var result = ResultadoApp<List<LotePagamentoLinhaOutput>>.OK(items);
        return Task.FromResult(result);
    }
}
