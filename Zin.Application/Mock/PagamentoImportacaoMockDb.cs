using Zin.Application.DTOs.Importacao.Pagamento;

namespace Zin.Application.Mock;

public static class PagamentoImportacaoMockDb
{
    public static List<LotePagamentoItemOutput> Lotes { get; } = new List<LotePagamentoItemOutput>
    {
        new LotePagamentoItemOutput
        {
            Id = 1,
            Data = new DateTime(2025, 9, 19, 10, 30, 0),
            Usuario = "<EMAIL>",
            NomeArquivo = "pagamentos_semana_01.xlsx",
            Lidos = 100,
            Programados = 50,
            Liquidados = 45,
            Criados = 5,
            Ignorados = 0,
            Duplicados = 2,
            Erros = 3,
            Status = "Concluído com erros"
        },
        new LotePagamentoItemOutput
        {
            Id = 2,
            Data = new DateTime(2025, 9, 18, 15, 0, 0),
            Usuario = "<EMAIL>",
            NomeArquivo = "liquidacoes_urgentes.xlsx",
            Lidos = 10,
            Programados = 0,
            Liquidados = 10,
            Criados = 0,
            Ignorados = 0,
            Duplicados = 0,
            Erros = 0,
            Status = "Concluído"
        }
    };

    public static List<LotePagamentoDetalheOutput> LoteDetalhes { get; } = new List<LotePagamentoDetalheOutput>
    {
        new LotePagamentoDetalheOutput
        {
            Id = 1,
            Data = new DateTime(2025, 9, 19, 10, 30, 0),
            Usuario = "<EMAIL>",
            NomeArquivo = "pagamentos_semana_01.xlsx",
            Status = "Concluído com erros",
            Lidos = 100,
            Programados = 50,
            Liquidados = 45,
            Criados = 5,
            Ignorados = 0,
            Duplicados = 2,
            Erros = 3
        }
    };

    public static List<LotePagamentoLinhaOutput> Linhas { get; } = new List<LotePagamentoLinhaOutput>
    {
        // Linhas para o Lote 1
        new LotePagamentoLinhaOutput
        {
            NumeroLinha = 2,
            CnpjPagador = "11.111.111/0001-11",
            CnpjCpfFavorecido = "22.222.222/0001-22",
            NumeroNF = "NF-1001",
            DataLiquidacao = new DateTime(2025, 9, 18),
            ValorPago = 1500.50m,
            OperacaoAplicada = "LIQUIDAR",
            Resultado = "OK",
            Mensagem = "Pagamento liquidado com sucesso.",
            PagamentoId = 101
        },
        new LotePagamentoLinhaOutput
        {
            NumeroLinha = 3,
            CnpjPagador = "11.111.111/0001-11",
            CnpjCpfFavorecido = "33.333.333/0001-33",
            NumeroNF = "NF-1002",
            DataProgramacao = new DateTime(2025, 9, 25),
            ValorPago = null,
            OperacaoAplicada = "PROGRAMAR",
            Resultado = "OK",
            Mensagem = "Pagamento programado com sucesso.",
            PagamentoId = 102
        },
        new LotePagamentoLinhaOutput
        {
            NumeroLinha = 4,
            CnpjPagador = "11.111.111/0001-11",
            CnpjCpfFavorecido = "44.444.444/0001-44",
            NumeroNF = "NF-1003",
            DataLiquidacao = new DateTime(2025, 9, 18),
            ValorPago = 0,
            OperacaoAplicada = "ERRO",
            Resultado = "ERRO",
            Mensagem = "ValorPago é obrigatório para liquidação.",
            PagamentoId = null
        }
    };
}
