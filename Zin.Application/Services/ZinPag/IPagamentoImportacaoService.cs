using Zin.Application.DTOs.Importacao.Pagamento;
using Zin.Application.Shared.Retorno;

namespace Zin.Application.Services.ZinPag;

public interface IPagamentoImportacaoService
{
    Task<ResultadoApp<List<LotePagamentoItemOutput>>> ListarLotesAsync(FiltroLoteInput filtro);
    Task<ResultadoApp<LotePagamentoDetalheOutput>> ObterDetalhesLoteAsync(int loteId);
    Task<ResultadoApp<List<LotePagamentoLinhaOutput>>> ListarLinhasDoLoteAsync(int loteId, FiltroLinhaInput filtro);
}
