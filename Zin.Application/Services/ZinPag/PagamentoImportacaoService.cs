
using AutoMapper;
using System.Linq;
using System.Threading.Tasks;
using Zin.Application.DTOs.Importacao.Pagamento;
using Zin.Application.Shared.Retorno;
using Zin.Domain.Repositorios.Importacoes;

namespace Zin.Application.Services.ZinPag
{
    public class PagamentoImportacaoService : IPagamentoImportacaoService
    {
        private readonly IImportacaoRepository _importacaoRepository;
        private readonly IImportacaoPagamentoLinhaRepository _linhaRepository;
        private readonly IMapper _mapper;

        public PagamentoImportacaoService(
            IImportacaoRepository importacaoRepository,
            IImportacaoPagamentoLinhaRepository linhaRepository,
            IMapper mapper)
        {
            _importacaoRepository = importacaoRepository;
            _linhaRepository = linhaRepository;
            _mapper = mapper;
        }

        public async Task<ResultadoApp<List<LotePagamentoItemOutput>>> ListarLotesAsync(FiltroLoteInput filtro)
        {
            var query = (await _importacaoRepository.BuscarAsync(x => x.Tipo == "PAGAMENTO")).AsQueryable();

            if (filtro.DataInicio.HasValue)
                query = query.Where(x => x.Data >= filtro.DataInicio.Value);
            if (filtro.DataFim.HasValue)
                query = query.Where(x => x.Data <= filtro.DataFim.Value);
            if (!string.IsNullOrEmpty(filtro.Status))
                query = query.Where(x => x.Status.ToString() == filtro.Status);
            if (!string.IsNullOrEmpty(filtro.Usuario))
                query = query.Where(x => x.IdCliente.Contains(filtro.Usuario));

            var items = query.OrderByDescending(x => x.Data).ToList();

            var dtos = _mapper.Map<List<LotePagamentoItemOutput>>(items);

            return ResultadoApp<List<LotePagamentoItemOutput>>.OK(dtos);
        }

        public async Task<ResultadoApp<LotePagamentoDetalheOutput>> ObterDetalhesLoteAsync(int loteId)
        {
            var lote = await _importacaoRepository.BuscarPorIdAsync(loteId);
            if (lote == null || lote.Tipo != "PAGAMENTO")
                return ResultadoApp<LotePagamentoDetalheOutput>.Falha(new ErroApp(TipoErroApp.NaoEncontrado, "LOTE_NAO_ENCONTRADO", "Lote não encontrado"));

            var dto = _mapper.Map<LotePagamentoDetalheOutput>(lote);
            return ResultadoApp<LotePagamentoDetalheOutput>.OK(dto);
        }

        public async Task<ResultadoApp<List<LotePagamentoLinhaOutput>>> ListarLinhasDoLoteAsync(int loteId, FiltroLinhaInput filtro)
        {
            var query = (await _linhaRepository.BuscarAsync(x => x.ImportacaoId == loteId)).AsQueryable();

            if (!string.IsNullOrEmpty(filtro.Status))
                query = query.Where(x => x.Resultado == filtro.Status);
            if (!string.IsNullOrEmpty(filtro.Operacao))
                query = query.Where(x => x.OperacaoAplicada == filtro.Operacao);
            if (!string.IsNullOrEmpty(filtro.NumeroNF))
                query = query.Where(x => x.NumeroNF.Contains(filtro.NumeroNF));
            if (!string.IsNullOrEmpty(filtro.Cnpj))
                query = query.Where(x => x.CnpjPagador.Contains(filtro.Cnpj) || x.CnpjCpfFavorecido.Contains(filtro.Cnpj));

            var items = query.OrderBy(x => x.NumeroLinha).ToList();

            return ResultadoApp<List<LotePagamentoLinhaOutput>>.OK(_mapper.Map<List<LotePagamentoLinhaOutput>>(items));
        }
    }
}
