using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class ImportacaoPagamentoLinhaRepository : IImportacaoPagamentoLinhaRepository
    {
        private readonly ZinDbContext _context;

        public ImportacaoPagamentoLinhaRepository(ZinDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<ImportacaoPagamentoLinha>> ListarAsync()
        {
            return await _context.ImportacoesPagamentosLinhas.ToListAsync();
        }

        public async Task<ImportacaoPagamentoLinha?> BuscarPorIdAsync(int id)
        {
            return await _context.ImportacoesPagamentosLinhas.FindAsync(id);
        }

        public async Task<IEnumerable<ImportacaoPagamentoLinha>> BuscarAsync(Expression<Func<ImportacaoPagamentoLinha, bool>> predicado)
        {
            return await _context.ImportacoesPagamentosLinhas.Where(predicado).ToListAsync();
        }

        public async Task<int> InserirAsync(ImportacaoPagamentoLinha entidade)
        {
            await _context.ImportacoesPagamentosLinhas.AddAsync(entidade);
            await _context.SaveChangesAsync();
            return entidade.Id;
        }

        public async Task<int[]> InserirVariosAsync(IEnumerable<ImportacaoPagamentoLinha> entidades)
        {
            await _context.ImportacoesPagamentosLinhas.AddRangeAsync(entidades);
            await _context.SaveChangesAsync();
            return entidades.Select(e => e.Id).ToArray();
        }

        public async Task AtualizarAsync(ImportacaoPagamentoLinha entidade)
        {
            _context.ImportacoesPagamentosLinhas.Update(entidade);
            await _context.SaveChangesAsync();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<ImportacaoPagamentoLinha> entidades)
        {
            throw new NotImplementedException();
        }
    }
}
