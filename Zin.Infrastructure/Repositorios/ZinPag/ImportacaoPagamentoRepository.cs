using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.ZinPag.Pagamentos;
using Zin.Domain.Repositorios.ZinPag;
using Zin.Infrastructure.Dados;

namespace Zin.Infrastructure.Repositorios.ZinPag
{
    public class ImportacaoPagamentoRepository : IImportacaoPagamentoRepository
    {
        private readonly ZinDbContext _context;

        public ImportacaoPagamentoRepository(ZinDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<ImportacaoPagamento>> ListarAsync()
        {
            return await _context.ImportacoesPagamentos.ToListAsync();
        }

        public async Task<ImportacaoPagamento?> BuscarPorIdAsync(int id)
        {
            return await _context.ImportacoesPagamentos.FindAsync(id);
        }

        public async Task<IEnumerable<ImportacaoPagamento>> BuscarAsync(Expression<Func<ImportacaoPagamento, bool>> predicado)
        {
            return await _context.ImportacoesPagamentos.Where(predicado).ToListAsync();
        }

        public async Task<int> InserirAsync(ImportacaoPagamento entidade)
        {
            await _context.ImportacoesPagamentos.AddAsync(entidade);
            await _context.SaveChangesAsync();
            return entidade.Id;
        }

        public async Task<int[]> InserirVariosAsync(IEnumerable<ImportacaoPagamento> entidades)
        {
            await _context.ImportacoesPagamentos.AddRangeAsync(entidades);
            await _context.SaveChangesAsync();
            return entidades.Select(e => e.Id).ToArray();
        }

        public async Task AtualizarAsync(ImportacaoPagamento entidade)
        {
            _context.ImportacoesPagamentos.Update(entidade);
            await _context.SaveChangesAsync();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<ImportacaoPagamento> entidades)
        {
            throw new NotImplementedException();
        }
    }
}
