using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.Importacoes;
using Zin.Domain.Repositorios.Importacoes;
using Zin.Infrastructure.Dados;

namespace Zin.Infrastructure.Repositorios.Importacoes;

public class ImportacaoPagamentoLinhaRepository : IImportacaoPagamentoLinhaRepository
{
    private readonly ZinImportacoesDbContext _context;

    public ImportacaoPagamentoLinhaRepository(ZinImportacoesDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<ImportacaoPagamentoLinha>> ListarAsync()
    {
        return await _context.ImportacaoPagamentoLinhas.ToListAsync();
    }

    public async Task<ImportacaoPagamentoLinha?> BuscarPorIdAsync(int id)
    {
        return await _context.ImportacaoPagamentoLinhas.FindAsync(id);
    }

    public async Task<IEnumerable<ImportacaoPagamentoLinha>> BuscarAsync(Expression<Func<ImportacaoPagamentoLinha, bool>> predicado)
    {
        return await _context.ImportacaoPagamentoLinhas.Where(predicado).ToListAsync();
    }

    public async Task<int> InserirAsync(ImportacaoPagamentoLinha entidade)
    {
        await _context.ImportacaoPagamentoLinhas.AddAsync(entidade);
        await _context.SaveChangesAsync();
        return entidade.Id;
    }

    public async Task<int[]> InserirVariosAsync(IEnumerable<ImportacaoPagamentoLinha> entidades)
    {
        await _context.ImportacaoPagamentoLinhas.AddRangeAsync(entidades);
        await _context.SaveChangesAsync();
        return entidades.Select(e => e.Id).ToArray();
    }

    public async Task AtualizarAsync(ImportacaoPagamentoLinha entidade)
    {
        _context.ImportacaoPagamentoLinhas.Update(entidade);
        await _context.SaveChangesAsync();
    }

    public Task DeletarAsync(int id)
    {
        throw new NotImplementedException();
    }

    public Task DeletarVariosAsync(IEnumerable<ImportacaoPagamentoLinha> entidades)
    {
        throw new NotImplementedException();
    }
}
