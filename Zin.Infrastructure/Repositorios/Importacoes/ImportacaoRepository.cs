using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Zin.Domain.Entidades.Importacoes;
using Zin.Domain.Repositorios.Importacoes;
using Zin.Infrastructure.Dados;

namespace Zin.Infrastructure.Repositorios.Importacoes
{
    public class ImportacaoRepository : IImportacaoRepository
    {
        private readonly ZinImportacoesDbContext _context;

        public ImportacaoRepository(ZinImportacoesDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Importacao>> ListarAsync()
        {
            return await _context.Importacoes.Include(x => x.Status).ToListAsync();
        }

        public async Task<Importacao?> BuscarPorIdAsync(int id)
        {
            return await _context.Importacoes.Include(x => x.Status).FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<IEnumerable<Importacao>> BuscarAsync(Expression<Func<Importacao, bool>> predicado)
        {
            return await _context.Importacoes.Where(predicado).Include(x => x.Status).ToListAsync();
        }

        public async Task<int> InserirAsync(Importacao entidade)
        {
            await _context.Importacoes.AddAsync(entidade);
            await _context.SaveChangesAsync();
            return entidade.Id;
        }

        public async Task<int[]> InserirVariosAsync(IEnumerable<Importacao> entidades)
        {
            await _context.Importacoes.AddRangeAsync(entidades);
            await _context.SaveChangesAsync();
            return entidades.Select(e => e.Id).ToArray();
        }

        public async Task AtualizarAsync(Importacao entidade)
        {
            _context.Importacoes.Update(entidade);
            await _context.SaveChangesAsync();
        }

        public Task DeletarAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task DeletarVariosAsync(IEnumerable<Importacao> entidades)
        {
            throw new NotImplementedException();
        }
    }
}