using Microsoft.EntityFrameworkCore;
using Zin.Domain.Entidades.Importacoes;

namespace Zin.Infrastructure.Dados
{
    public class ZinImportacoesDbContext(DbContextOptions<ZinImportacoesDbContext> options) : DbContext(options)
    {
        // Define DbSet properties for the entities related to importações
        public DbSet<ImportacaoAgregador> ImportacaoAgregadores { get; set; }
        public DbSet<Importacao> Importacoes { get; set; }
        public DbSet<ImportacaoPagamentoLinha> ImportacaoPagamentoLinhas { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.HasDefaultSchema("importacoes");
            modelBuilder.Entity<ImportacaoAgregador>()
            .Property(e => e.DadosAgregador)
            .HasColumnType("jsonb");
        }
    }
}
