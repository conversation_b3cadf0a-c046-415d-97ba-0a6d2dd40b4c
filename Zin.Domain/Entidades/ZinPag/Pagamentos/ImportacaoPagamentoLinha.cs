using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Zin.Domain.Entidades.ZinPag.Pagamentos;

[Table("importacoes_pagamentos_linhas", Schema = "zinpag")]
public class ImportacaoPagamentoLinha
{
    [Key]
    [Column("id")]
    public int Id { get; set; }
    
    [Column("id_importacao_pagamento")]
    public int ImportacaoPagamentoId { get; set; }
    
    [Column("numero_linha")]
    public int NumeroLinha { get; set; }
    
    [Column("cnpj_pagador")]
    [MaxLength(18)]
    public string CnpjPagador { get; set; }
    
    [Column("cnpj_cpf_favorecido")]
    [MaxLength(18)]
    public string CnpjCpfFavorecido { get; set; }
    
    [Column("numero_nf")]
    [MaxLength(50)]
    public string NumeroNF { get; set; }
    
    [Column("data_programacao")]
    public DateTime? DataProgramacao { get; set; }
    
    [Column("data_liquidacao")]
    public DateTime? DataLiquidacao { get; set; }
    
    [Column("valor_pago", TypeName = "decimal(10,2)")]
    public decimal? ValorPago { get; set; }
    
    [Column("operacao_aplicada")]
    [MaxLength(20)]
    public string OperacaoAplicada { get; set; }
    
    [Column("resultado")]
    [MaxLength(20)]
    public string Resultado { get; set; }
    
    [Column("mensagem")]
    [MaxLength(500)]
    public string Mensagem { get; set; }
    
    [Column("id_pagamento")]
    public int? PagamentoId { get; set; }

    // Navigation properties
    [ForeignKey(nameof(ImportacaoPagamentoId))]
    public virtual ImportacaoPagamento ImportacaoPagamento { get; set; }
    
    [ForeignKey(nameof(PagamentoId))]
    public virtual Pagamento? Pagamento { get; set; }
}
