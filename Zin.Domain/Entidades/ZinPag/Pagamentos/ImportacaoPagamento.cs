using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.ZinPag.Pagamentos;

[Table("importacoes_pagamentos", Schema = "zinpag")]
public class ImportacaoPagamento
{
    [Key]
    [Column("id")]
    public int Id { get; set; }
    
    [Column("nome_arquivo")]
    [MaxLength(255)]
    public string NomeArquivo { get; set; }
    
    [Column("url_arquivo")]
    [MaxLength(500)]
    public string UrlArquivo { get; set; }
    
    [Column("id_cliente")]
    [MaxLength(50)]
    public string IdCliente { get; set; }
    
    [Column("data")]
    public DateTime Data { get; set; }
    
    [Column("status")]
    public StatusImportacao Status { get; set; }
    
    [Column("mensagem")]
    [MaxLength(1000)]
    public string Mensagem { get; set; }

    // Campos específicos para importação de pagamentos
    [Column("lidos")]
    public int Lidos { get; set; }
    
    [Column("programados")]
    public int Programados { get; set; }
    
    [Column("liquidados")]
    public int Liquidados { get; set; }
    
    [Column("criados")]
    public int Criados { get; set; }
    
    [Column("ignorados")]
    public int Ignorados { get; set; }
    
    [Column("duplicados")]
    public int Duplicados { get; set; }
    
    [Column("erros")]
    public int Erros { get; set; }

    // Navigation property
    public ICollection<ImportacaoPagamentoLinha> Linhas { get; set; } = [];
}
