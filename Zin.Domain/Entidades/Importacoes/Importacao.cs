using Zin.Domain.Enums;

namespace Zin.Domain.Entidades.Importacoes;

public class Importacao
{
    public int Id { get; set; }
    public string NomeArquivo { get; set; }
    public string UrlArquivo { get; set; }
    public string IdCliente { get; set; }
    public DateTime Data { get; set; }
    public StatusImportacao Status { get; set; }
    public string Mensagem { get; set; }

    // Campos para a importação de pagamentos
    public int Lidos { get; set; }
    public int Programados { get; set; }
    public int Liquidados { get; set; }
    public int Criados { get; set; }
    public int Ignorados { get; set; }
    public int Duplicados { get; set; }
    public int Erros { get; set; }
    public string Tipo { get; set; } // "PAGAMENTO"
}
